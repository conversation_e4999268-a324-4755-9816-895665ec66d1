"use client";
import React, { useState, useEffect } from "react";

type FormState = "idle" | "loading" | "success";

import {
  PopoverForm,
  PopoverFormButton,
  PopoverFormCutOutLeftIcon,
  PopoverFormCutOutRightIcon,
  PopoverFormSeparator,
  PopoverFormSuccess,
} from "./popover-form";

export default function ContactMeButton() {
  const [formState, setFormState] = useState<FormState>("idle");
  const [open, setOpen] = useState(false);
  const [feedback, setFeedback] = useState("");

  function submit() {
    setFormState("loading");
    setTimeout(() => {
      setFormState("success");
    }, 1500);

    setTimeout(() => {
      setOpen(false);
      setFormState("idle");
      setFeedback("");
    }, 3300);
  }

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        setOpen(false);
      }

      if (
        (event.ctrlKey || event.metaKey) &&
        event.key === "Enter" &&
        open &&
        formState === "idle"
      ) {
        submit();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [open, formState]);

  return (
    <>
      {/* Simple button for navbar */}
      <button
        onClick={() => setOpen(true)}
        className="px-3 py-1 text-sm font-medium bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
      >
        Contact Me
      </button>

      {/* Popup form */}
      {open && (
        <PopoverForm
          title="Contact Me"
          open={open}
          setOpen={setOpen}
          width="364px"
          height="192px"
          showCloseButton={formState !== "success"}
          showSuccess={formState === "success"}
          openChild={
            <form
              onSubmit={(e) => {
                e.preventDefault();
                if (!feedback) return;
                submit();
              }}
            >
              <div className="relative">
                <textarea
                  autoFocus
                  placeholder="Your message..."
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  className="h-32 w-full resize-none rounded-t-lg p-3 text-sm outline-none"
                  required
                />
              </div>
              <div className="relative flex h-12 items-center px-[10px]">
                <PopoverFormSeparator />
                <div className="absolute left-0 top-0 -translate-x-[1.5px] -translate-y-1/2">
                  <PopoverFormCutOutLeftIcon />
                </div>
                <div className="absolute right-0 top-0 translate-x-[1.5px] -translate-y-1/2 rotate-180">
                  <PopoverFormCutOutRightIcon />
                </div>
                <PopoverFormButton
                  loading={formState === "loading"}
                  text="Send"
                />
              </div>
            </form>
          }
          successChild={
            <PopoverFormSuccess
              title="Message Sent!"
              description="Thank you for reaching out! I'll get back to you soon."
            />
          }
        />
      )}
    </>
  );
}
