'use client'
import React, { useState, useRef, useCallback } from "react";
import FlipLink from "./text-effect-flipper";
import { useGSAP } from "@gsap/react";
import gsap from "gsap";

const MENU_ITEMS = [
  { href: "/", label: "Home" },
  { href: "#about", label: "About" },
  { href: "#projects", label: "Projects" },
  { href: "#skills", label: "Skills" }
];

const MenuButton = ({ isOpen, onClick }) => (
  <button 
    onClick={onClick}
    className="text-4xl hover:text-gray-300 transition-colors"
    aria-label="Toggle menu"
  >
    {isOpen ? '×' : '☰'}
  </button>
);

const MenuItem = ({ href, label, ref }) => (
  <li ref={ref}>
    <a className="text-6xl font-semibold inline-block 
             leading-tight py-2
             transition-all duration-300 ease-in-out 
             hover:text-red-600 hover:scale-110">
      <FlipLink href={href}>{label}</FlipLink>
    </a>
  </li>
);

export const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const menuRef = useRef(null);
  const menuItemsRef = useRef([]);

  const handleMenuAnimation = useCallback(() => {
    if (isMenuOpen) {
      gsap.fromTo(menuRef.current,
        { opacity: 0 },
        { opacity: 1, duration: 0.5, ease: "power2.inOut" }
      );

      gsap.fromTo(menuItemsRef.current,
        { y: -50, opacity: 0 },
        {
          y: 0,
          opacity: 1,
          duration: 0.6,
          stagger: 0.1,
          ease: "back.out(1.7)"
        }
      );
    }
  }, [isMenuOpen]);

  useGSAP(() => {
    handleMenuAnimation();
  }, [isMenuOpen, handleMenuAnimation]);

  const handleMenuClose = useCallback(() => {
    gsap.to(menuRef.current, {
      opacity: 0,
      duration: 0.4,
      ease: "power2.inOut",
      onComplete: () => setIsMenuOpen(false)
    });
  }, []);

  const toggleMenu = useCallback(() => {
    setIsMenuOpen(prev => !prev);
  }, []);

  return (
    <>
      <div className="flex justify-between items-center p-4 fixed w-full top-0 z-50">
        <div className="text-2xl font-bold">
          <a href="/">Portfolio</a>
        </div>
        <MenuButton isOpen={isMenuOpen} onClick={toggleMenu} />
      </div>

      {isMenuOpen && (
        <div ref={menuRef} className="fixed inset-0 bg-white bg-opacity-95 z-40 flex items-center justify-center">
          <nav className="text-center">
            <ul className="flex flex-col gap-8">
              {MENU_ITEMS.map((item, index) => (
                <MenuItem
                  key={item.href}
                  href={item.href}
                  label={item.label}
                  ref={el => menuItemsRef.current[index] = el}
                />
              ))}
            </ul>
          </nav>
        </div>
      )}
    </>
  );
};
