"use client";
import React, { useState, useRef, useCallback } from "react";
import FlipLink from "./text-effect-flipper";
import { useGS<PERSON> } from "@gsap/react";
import gsap from "gsap";
import ThemeToggleButton from "./theme-toggle-button";
import ContactMeButton from "./contactMeButton";

const MENU_ITEMS = [
  { href: "/", label: "Home" },
  { href: "#about", label: "About" },
  { href: "#projects", label: "Projects" },
  { href: "#skills", label: "Skills" },
];

const MenuButton = ({ isOpen, onClick }) => (
  <button
    onClick={onClick}
    className="text-2xl sm:text-3xl md:text-4xl hover:text-black-300 transition-colors p-2"
    aria-label="Toggle menu"
  >
    {isOpen ? "×" : "☰"}
  </button>
);

// eslint-disable-next-line react/display-name
const MenuItem = React.forwardRef(({ href, label }, ref) => (
  <li ref={ref} className="relative group">
    <a
      className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl
             font-semibold inline-block
             leading-normal py-2 sm:py-3 md:py-4
             transition-all duration-300 ease-in-out
             text-black dark:text-white
             hover:text-red-800 dark:hover:text-red-400 hover:scale-110
             font-[skiper] text-center"
    >
      <FlipLink href={href}>{label}</FlipLink>
    </a>
    {/* Bottom border */}
    <span
      className="absolute left-0 bottom-0 w-0 h-[3px] bg-red-600 dark:bg-red-400
                 transition-all duration-300 ease-in-out group-hover:w-full"
    ></span>
  </li>
));

export const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isMenuVisible, setIsMenuVisible] = useState(false); // Track visibility
  const menuRef = useRef(null);
  const menuItemsRef = useRef([]);

  const openMenuAnimation = useCallback(() => {
    gsap.fromTo(
      menuRef.current,
      { opacity: 0 },
      { opacity: 1, duration: 0.5, ease: "power2.inOut" }
    );

    gsap.fromTo(
      menuItemsRef.current,
      { y: -50, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 0.6,
        stagger: 0.1,
        ease: "back.out(1.7)",
      }
    );
  }, []);

  const closeMenuAnimation = useCallback(() => {
    gsap.to(menuItemsRef.current, {
      y: -50,
      opacity: 0,
      duration: 0.4,
      stagger: 0.1,
      ease: "power2.in",
    });

    gsap.to(menuRef.current, {
      opacity: 0,
      duration: 0.5,
      ease: "power2.inOut",
      delay: 0.3,
      onComplete: () => {
        setIsMenuVisible(false);
      },
    });
  }, []);

  const toggleMenu = useCallback(() => {
    if (!isMenuOpen) {
      setIsMenuVisible(true);
      setIsMenuOpen(true);
    } else {
      setIsMenuOpen(false);
      closeMenuAnimation();
    }
  }, [isMenuOpen, closeMenuAnimation]);

  useGSAP(() => {
    if (isMenuOpen) {
      openMenuAnimation();
    }
  }, [isMenuOpen, openMenuAnimation]);

  return (
    <>
      <div
        className={`flex justify-between items-center px-4 sm:px-6 lg:px-8 py-3 sm:py-4
                   fixed w-full top-0 z-50 transition-opacity duration-300 ${
                     isMenuOpen
                       ? "opacity-0 pointer-events-none"
                       : "opacity-100"
                   }`}
      >
        <div className="text-lg sm:text-xl md:text-2xl font-bold">
          <a href="/">Portfolio</a>
        </div>

        <div className="flex items-center gap-2 sm:gap-3 md:gap-4">
          {/* Hide contact button on small screens */}
          <div className="hidden sm:block">
            <ContactMeButton />
          </div>
          <ThemeToggleButton />
          <MenuButton isOpen={isMenuOpen} onClick={toggleMenu} />
        </div>
      </div>

      {/* Menu button overlay - always visible with responsive positioning */}
      <div className="fixed top-3 right-4 sm:top-4 sm:right-4 md:right-6 lg:right-8 z-[60]">
        <MenuButton isOpen={isMenuOpen} onClick={toggleMenu} />
      </div>

      {isMenuVisible && (
        <div
          ref={menuRef}
          className="fixed inset-0 bg-white dark:bg-zinc-900 bg-opacity-95 dark:bg-opacity-95 z-40
                     flex items-center justify-center px-4 sm:px-6 lg:px-8"
        >
          <nav className="text-center w-full max-w-2xl">
            <ul className="flex flex-col gap-6 sm:gap-8 md:gap-10 lg:gap-12">
              {MENU_ITEMS.map((item, index) => (
                <MenuItem
                  key={item.href}
                  href={item.href}
                  label={item.label}
                  ref={(el) => (menuItemsRef.current[index] = el)}
                />
              ))}

              {/* Show contact button in menu on small screens */}
              <li className="sm:hidden mt-4">
                <div className="flex justify-center">
                  <ContactMeButton />
                </div>
              </li>
            </ul>
          </nav>
        </div>
      )}
    </>
  );
};
