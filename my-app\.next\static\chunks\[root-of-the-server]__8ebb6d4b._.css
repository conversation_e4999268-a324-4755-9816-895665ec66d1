/* [next]/internal/font/google/geist_e531dabc.module.css [app-client] (css) */
@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/973faccb4f6aedb5-s.b7d310ad.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/d26cc22533d232c7-s.81df3a5b.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/b0a57561b6cb5495-s.p.da1ebef7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Geist Fallback;
  src: local(Arial);
  ascent-override: 95.94%;
  descent-override: 28.16%;
  line-gap-override: 0.0%;
  size-adjust: 104.76%;
}

.geist_e531dabc-module__QGiZLq__className {
  font-family: Geist, Geist Fallback;
  font-style: normal;
}

.geist_e531dabc-module__QGiZLq__variable {
  --font-geist-sans: "Geist", "Geist Fallback";
}

/* [next]/internal/font/google/geist_mono_68a01160.module.css [app-client] (css) */
@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/942c7eecbf9bc714-s.cb6bbcb1.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/e5e2a9f48cda0a81-s.e32db976.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/8ee3a1ba4ed5baee-s.p.be19f591.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Geist Mono Fallback;
  src: local(Arial);
  ascent-override: 74.67%;
  descent-override: 21.92%;
  line-gap-override: 0.0%;
  size-adjust: 134.59%;
}

.geist_mono_68a01160-module__YLcDdW__className {
  font-family: Geist Mono, Geist Mono Fallback;
  font-style: normal;
}

.geist_mono_68a01160-module__YLcDdW__variable {
  --font-geist-mono: "Geist Mono", "Geist Mono Fallback";
}

/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: rgba(0, 0, 0, 0);
      --tw-gradient-via: rgba(0, 0, 0, 0);
      --tw-gradient-to: rgba(0, 0, 0, 0);
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-content: "";
      --tw-animation-delay: 0s;
      --tw-animation-direction: normal;
      --tw-animation-duration: initial;
      --tw-animation-fill-mode: none;
      --tw-animation-iteration-count: 1;
      --tw-enter-opacity: 1;
      --tw-enter-rotate: 0;
      --tw-enter-scale: 1;
      --tw-enter-translate-x: 0;
      --tw-enter-translate-y: 0;
      --tw-exit-opacity: 1;
      --tw-exit-rotate: 0;
      --tw-exit-scale: 1;
      --tw-exit-translate-x: 0;
      --tw-exit-translate-y: 0;
    }

    ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: rgba(0, 0, 0, 0);
      --tw-gradient-via: rgba(0, 0, 0, 0);
      --tw-gradient-to: rgba(0, 0, 0, 0);
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-content: "";
      --tw-animation-delay: 0s;
      --tw-animation-direction: normal;
      --tw-animation-duration: initial;
      --tw-animation-fill-mode: none;
      --tw-animation-iteration-count: 1;
      --tw-enter-opacity: 1;
      --tw-enter-rotate: 0;
      --tw-enter-scale: 1;
      --tw-enter-translate-x: 0;
      --tw-enter-translate-y: 0;
      --tw-exit-opacity: 1;
      --tw-exit-rotate: 0;
      --tw-exit-scale: 1;
      --tw-exit-translate-x: 0;
      --tw-exit-translate-y: 0;
    }
  }
}

@layer theme {
  :root, :host {
    --color-red-400: #ff6568;
    --color-red-600: #e40014;
    --color-red-800: #9f0712;
    --color-amber-500: #f99c00;
    --color-yellow-300: #ffe02a;
    --color-cyan-500: #00b7d7;
    --color-blue-600: #155dfc;
    --color-blue-700: #1447e6;
    --color-indigo-300: #a4b3ff;
    --color-indigo-500: #625fff;
    --color-violet-500: #8d54ff;
    --color-rose-300: #ffa2ae;
    --color-rose-500: #ff2357;
    --color-gray-700: #364153;
    --color-gray-800: #1e2939;
    --color-gray-900: #101828;
    --color-zinc-900: #18181b;
    --color-neutral-50: #fafafa;
    --color-neutral-100: #f5f5f5;
    --color-neutral-500: #737373;
    --color-neutral-800: #262626;
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-xs: 20rem;
    --container-xl: 36rem;
    --container-3xl: 48rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --text-7xl: 4.5rem;
    --text-7xl--line-height: 1;
    --text-8xl: 6rem;
    --text-8xl--line-height: 1;
    --font-weight-light: 300;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-tight: -.025em;
    --tracking-wide: .025em;
    --leading-tight: 1.25;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --ease-in-out: cubic-bezier(.4, 0, .2, 1);
    --animate-spin: spin 1s linear infinite;
    --blur-3xl: 64px;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
  }

  @supports (color: color(display-p3 0 0 0)) {
    :root, :host {
      --color-red-400: color(display-p3 .933534 .431676 .423491);
      --color-red-600: color(display-p3 .830323 .140383 .133196);
      --color-red-800: color(display-p3 .569606 .121069 .108493);
      --color-amber-500: color(display-p3 .93994 .620584 .0585367);
      --color-yellow-300: color(display-p3 .982669 .880884 .32102);
      --color-cyan-500: color(display-p3 .246703 .710032 .841444);
      --color-blue-600: color(display-p3 .174493 .358974 .950247);
      --color-blue-700: color(display-p3 .1379 .274983 .867624);
      --color-indigo-300: color(display-p3 .650892 .700156 .990824);
      --color-indigo-500: color(display-p3 .380374 .372235 .971707);
      --color-violet-500: color(display-p3 .523372 .329605 .990884);
      --color-rose-300: color(display-p3 .96017 .647703 .683715);
      --color-rose-500: color(display-p3 .921824 .240748 .355666);
      --color-gray-700: color(display-p3 .219968 .253721 .318679);
      --color-gray-800: color(display-p3 .125854 .159497 .216835);
      --color-gray-900: color(display-p3 .070423 .0928982 .151928);
      --color-zinc-900: color(display-p3 .0937957 .093793 .104806);
      --color-neutral-50: color(display-p3 .980256 .980256 .980256);
      --color-neutral-100: color(display-p3 .960587 .960587 .960587);
      --color-neutral-500: color(display-p3 .451519 .451519 .451519);
      --color-neutral-800: color(display-p3 .149382 .149382 .149382);
    }
  }

  @supports (color: lab(0% 0 0)) {
    :root, :host {
      --color-red-400: lab(63.7053% 60.7449 31.3109);
      --color-red-600: lab(48.4493% 77.4328 61.5452);
      --color-red-800: lab(33.7174% 55.8993 41.0293);
      --color-amber-500: lab(72.7183% 31.8672 97.9407);
      --color-yellow-300: lab(89.7033% -.480324 84.4917);
      --color-cyan-500: lab(67.805% -35.3952 -30.2018);
      --color-blue-600: lab(44.0605% 29.0279 -86.0352);
      --color-blue-700: lab(36.9089% 35.0961 -85.6872);
      --color-indigo-300: lab(74.0235% 8.54138 -41.6075);
      --color-indigo-500: lab(48.295% 38.3129 -81.9673);
      --color-violet-500: lab(49.9355% 55.1777 -81.8963);
      --color-rose-300: lab(76.6339% 38.3549 9.68834);
      --color-rose-500: lab(56.101% 79.4329 31.4532);
      --color-gray-700: lab(27.1134% -.956401 -12.3224);
      --color-gray-800: lab(16.1051% -1.18239 -11.7533);
      --color-gray-900: lab(8.11897% .811279 -12.254);
      --color-zinc-900: lab(8.30603% .618212 -2.16573);
      --color-neutral-50: lab(98.26% 0 0);
      --color-neutral-100: lab(96.52% -.0000596046 0);
      --color-neutral-500: lab(48.496% 0 0);
      --color-neutral-800: lab(15.204% 0 0);
    }
  }
}

@layer base {
  *, :after, :before {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::backdrop {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::-webkit-file-upload-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  ::-webkit-file-upload-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:-moz-any([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup option:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:-moz-any([multiple], [size])) optgroup option:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:is([multiple], [size])) optgroup option:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup option:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :where(select:-moz-any([multiple], [size])) optgroup option:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :where(select:is([multiple], [size])) optgroup option:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-file-upload-button {
    margin-right: 4px;
  }

  :not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::file-selector-button {
    margin-right: 4px;
  }

  :not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::file-selector-button {
    margin-right: 4px;
  }

  :-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-file-upload-button {
    margin-left: 4px;
  }

  :-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::file-selector-button {
    margin-left: 4px;
  }

  :is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::file-selector-button {
    margin-left: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-year-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-month-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-day-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-hour-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-minute-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-second-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-millisecond-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-meridiem-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  input:where([type="button"], [type="reset"], [type="submit"]) {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::-webkit-file-upload-button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::file-selector-button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::-webkit-inner-spin-button {
    height: auto;
  }

  ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }

  * {
    border-color: var(--border);
    outline-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    * {
      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  body {
    background-color: var(--background);
    color: var(--foreground);
  }
}

@layer components;

@layer utilities {
  .pointer-events-none {
    pointer-events: none;
  }

  .visible {
    visibility: visible;
  }

  .sr-only {
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .-top-10 {
    top: calc(var(--spacing) * -10);
  }

  .-top-\[5px\] {
    top: -5px;
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-\[-1px\] {
    top: -1px;
  }

  .top-\[5\%\] {
    top: 5%;
  }

  .top-\[10\%\] {
    top: 10%;
  }

  .top-\[15\%\] {
    top: 15%;
  }

  .top-\[17px\] {
    top: 17px;
  }

  .top-\[70\%\] {
    top: 70%;
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .right-\[-5\%\] {
    right: -5%;
  }

  .right-\[15\%\] {
    right: 15%;
  }

  .-bottom-10 {
    bottom: calc(var(--spacing) * -10);
  }

  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }

  .bottom-\[5\%\] {
    bottom: 5%;
  }

  .left-0 {
    left: calc(var(--spacing) * 0);
  }

  .left-1\/2 {
    left: 50%;
  }

  .left-4 {
    left: calc(var(--spacing) * 4);
  }

  .left-\[-10\%\] {
    left: -10%;
  }

  .left-\[5\%\] {
    left: 5%;
  }

  .left-\[20\%\] {
    left: 20%;
  }

  .z-10 {
    z-index: 10;
  }

  .z-20 {
    z-index: 20;
  }

  .z-40 {
    z-index: 40;
  }

  .z-50 {
    z-index: 50;
  }

  .container {
    width: 100%;
  }

  @media (min-width: 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (min-width: 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (min-width: 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (min-width: 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (min-width: 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }

  .mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .-mt-1 {
    margin-top: calc(var(--spacing) * -1);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-100 {
    margin-top: calc(var(--spacing) * 100);
  }

  .mt-\[1px\] {
    margin-top: 1px;
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .ml-auto {
    margin-left: auto;
  }

  .block {
    display: block;
  }

  .flex {
    display: flex;
  }

  .hidden {
    display: none;
  }

  .inline-block {
    display: inline-block;
  }

  .inline-flex {
    display: inline-flex;
  }

  .size-3 {
    width: calc(var(--spacing) * 3);
    height: calc(var(--spacing) * 3);
  }

  .size-9 {
    width: calc(var(--spacing) * 9);
    height: calc(var(--spacing) * 9);
  }

  .size-\[1\.2rem\] {
    width: 1.2rem;
    height: 1.2rem;
  }

  .size-full {
    width: 100%;
    height: 100%;
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-9 {
    height: calc(var(--spacing) * 9);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-15 {
    height: calc(var(--spacing) * 15);
  }

  .h-32 {
    height: calc(var(--spacing) * 32);
  }

  .h-\[3px\] {
    height: 3px;
  }

  .h-\[6px\] {
    height: 6px;
  }

  .h-\[26px\] {
    height: 26px;
  }

  .h-\[190px\] {
    height: 190px;
  }

  .h-full {
    height: 100%;
  }

  .min-h-\[300px\] {
    min-height: 300px;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-0 {
    width: calc(var(--spacing) * 0);
  }

  .w-9 {
    width: calc(var(--spacing) * 9);
  }

  .w-20 {
    width: calc(var(--spacing) * 20);
  }

  .w-26 {
    width: calc(var(--spacing) * 26);
  }

  .w-\[10px\] {
    width: 10px;
  }

  .w-\[12px\] {
    width: 12px;
  }

  .w-fit {
    width: -moz-fit-content;
    width: fit-content;
  }

  .w-full {
    width: 100%;
  }

  .max-w-3xl {
    max-width: var(--container-3xl);
  }

  .max-w-xl {
    max-width: var(--container-xl);
  }

  .max-w-xs {
    max-width: var(--container-xs);
  }

  .shrink-0 {
    flex-shrink: 0;
  }

  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-x-\[1\.5px\] {
    --tw-translate-x: calc(1.5px * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-\[1\.5px\] {
    --tw-translate-x: 1.5px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .scale-0 {
    --tw-scale-x: 0%;
    --tw-scale-y: 0%;
    --tw-scale-z: 0%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-100 {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .rotate-0 {
    rotate: none;
  }

  .rotate-90 {
    rotate: 90deg;
  }

  .rotate-180 {
    rotate: 180deg;
  }

  .transform {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .resize-none {
    resize: none;
  }

  .flex-col {
    flex-direction: column;
  }

  .items-center {
    align-items: center;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-12 {
    gap: calc(var(--spacing) * 12);
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .rounded-\[16px\] {
    border-radius: 16px;
  }

  .rounded-\[20px\] {
    border-radius: 20px;
  }

  .rounded-\[24px\] {
    border-radius: 24px;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius);
  }

  .rounded-md {
    border-radius: calc(var(--radius)  - 2px);
  }

  .rounded-xl {
    border-radius: calc(var(--radius)  + 4px);
  }

  .rounded-t-lg {
    border-top-left-radius: var(--radius);
    border-top-right-radius: var(--radius);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-black\/\[0\.15\] {
    border-color: rgba(0, 0, 0, .15);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-black\/\[0\.15\] {
      border-color: color-mix(in oklab, var(--color-black) 15%, transparent);
    }
  }

  .border-transparent {
    border-color: rgba(0, 0, 0, 0);
  }

  .border-white\/\[0\.08\] {
    border-color: rgba(255, 255, 255, .08);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-white\/\[0\.08\] {
      border-color: color-mix(in oklab, var(--color-white) 8%, transparent);
    }
  }

  .border-white\/\[0\.15\] {
    border-color: rgba(255, 255, 255, .15);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-white\/\[0\.15\] {
      border-color: color-mix(in oklab, var(--color-white) 15%, transparent);
    }
  }

  .bg-\[\#030303\] {
    background-color: #030303;
  }

  .bg-background {
    background-color: var(--background);
  }

  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }

  .bg-destructive {
    background-color: var(--destructive);
  }

  .bg-muted {
    background-color: var(--muted);
  }

  .bg-neutral-50 {
    background-color: var(--color-neutral-50);
  }

  .bg-primary {
    background-color: var(--primary);
  }

  .bg-red-600 {
    background-color: var(--color-red-600);
  }

  .bg-secondary {
    background-color: var(--secondary);
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-white\/\[0\.03\] {
    background-color: rgba(255, 255, 255, .03);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/\[0\.03\] {
      background-color: color-mix(in oklab, var(--color-white) 3%, transparent);
    }
  }

  .bg-yellow-300 {
    background-color: var(--color-yellow-300);
  }

  .bg-gradient-to-b {
    --tw-gradient-position: to bottom in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-t {
    --tw-gradient-position: to top in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .from-\[\#030303\] {
    --tw-gradient-from: #030303;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-amber-500\/\[0\.15\] {
    --tw-gradient-from: rgba(249, 156, 0, .15);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-amber-500\/\[0\.15\] {
      --tw-gradient-from: color-mix(in oklab, var(--color-amber-500) 15%, transparent);
    }
  }

  .from-amber-500\/\[0\.15\] {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-cyan-500\/\[0\.15\] {
    --tw-gradient-from: rgba(0, 183, 215, .15);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-cyan-500\/\[0\.15\] {
      --tw-gradient-from: color-mix(in oklab, var(--color-cyan-500) 15%, transparent);
    }
  }

  .from-cyan-500\/\[0\.15\] {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-indigo-300 {
    --tw-gradient-from: var(--color-indigo-300);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-indigo-500\/\[0\.05\] {
    --tw-gradient-from: rgba(98, 95, 255, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-indigo-500\/\[0\.05\] {
      --tw-gradient-from: color-mix(in oklab, var(--color-indigo-500) 5%, transparent);
    }
  }

  .from-indigo-500\/\[0\.05\] {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-indigo-500\/\[0\.15\] {
    --tw-gradient-from: rgba(98, 95, 255, .15);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-indigo-500\/\[0\.15\] {
      --tw-gradient-from: color-mix(in oklab, var(--color-indigo-500) 15%, transparent);
    }
  }

  .from-indigo-500\/\[0\.15\] {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-primary\/90 {
    --tw-gradient-from: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-primary\/90 {
      --tw-gradient-from: color-mix(in oklab, var(--primary) 90%, transparent);
    }
  }

  .from-primary\/90 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-rose-500\/\[0\.15\] {
    --tw-gradient-from: rgba(255, 35, 87, .15);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-rose-500\/\[0\.15\] {
      --tw-gradient-from: color-mix(in oklab, var(--color-rose-500) 15%, transparent);
    }
  }

  .from-rose-500\/\[0\.15\] {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-violet-500\/\[0\.15\] {
    --tw-gradient-from: rgba(141, 84, 255, .15);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-violet-500\/\[0\.15\] {
      --tw-gradient-from: color-mix(in oklab, var(--color-violet-500) 15%, transparent);
    }
  }

  .from-violet-500\/\[0\.15\] {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-white {
    --tw-gradient-from: var(--color-white);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-white\/\[0\.08\] {
    --tw-gradient-from: rgba(255, 255, 255, .08);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-white\/\[0\.08\] {
      --tw-gradient-from: color-mix(in oklab, var(--color-white) 8%, transparent);
    }
  }

  .from-white\/\[0\.08\] {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .via-transparent {
    --tw-gradient-via: transparent;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-white\/90 {
    --tw-gradient-via: rgba(255, 255, 255, .9);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .via-white\/90 {
      --tw-gradient-via: color-mix(in oklab, var(--color-white) 90%, transparent);
    }
  }

  .via-white\/90 {
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .to-\[\#030303\]\/80 {
    --tw-gradient-to: rgba(3, 3, 3, .8);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color(display-p3 0 0 0)) {
    .to-\[\#030303\]\/80 {
      --tw-gradient-to: color(display-p3 .0117647 .0117647 .0117647 / .8);
    }
  }

  @supports (color: lab(0% 0 0)) {
    .to-\[\#030303\]\/80 {
      --tw-gradient-to: lab(.822525% 0 0 / .8);
    }
  }

  .to-primary {
    --tw-gradient-to: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-rose-300 {
    --tw-gradient-to: var(--color-rose-300);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-rose-500\/\[0\.05\] {
    --tw-gradient-to: rgba(255, 35, 87, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-rose-500\/\[0\.05\] {
      --tw-gradient-to: color-mix(in oklab, var(--color-rose-500) 5%, transparent);
    }
  }

  .to-rose-500\/\[0\.05\] {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-transparent {
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-white\/80 {
    --tw-gradient-to: rgba(255, 255, 255, .8);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-white\/80 {
      --tw-gradient-to: color-mix(in oklab, var(--color-white) 80%, transparent);
    }
  }

  .to-white\/80 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .bg-clip-text {
    -webkit-background-clip: text;
    background-clip: text;
  }

  .fill-muted {
    fill: var(--muted);
  }

  .stroke-border {
    stroke: var(--border);
  }

  .object-cover {
    object-fit: cover;
  }

  .p-0 {
    padding: calc(var(--spacing) * 0);
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .px-\[10px\] {
    padding-left: 10px;
    padding-right: 10px;
  }

  .py-0\.5 {
    padding-block: calc(var(--spacing) * .5);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .pt-0 {
    padding-top: calc(var(--spacing) * 0);
  }

  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }

  .text-center {
    text-align: center;
  }

  .font-\[skiper\] {
    font-family: skiper;
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-6xl {
    font-size: var(--text-6xl);
    line-height: var(--tw-leading, var(--text-6xl--line-height));
  }

  .text-7xl {
    font-size: var(--text-7xl);
    line-height: var(--tw-leading, var(--text-7xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .leading-normal {
    --tw-leading: var(--leading-normal);
    line-height: var(--leading-normal);
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-light {
    --tw-font-weight: var(--font-weight-light);
    font-weight: var(--font-weight-light);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }

  .tracking-wide {
    --tw-tracking: var(--tracking-wide);
    letter-spacing: var(--tracking-wide);
  }

  .text-pretty {
    text-wrap: pretty;
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .text-black {
    color: var(--color-black);
  }

  .text-foreground {
    color: var(--foreground);
  }

  .text-muted-foreground {
    color: var(--muted-foreground);
  }

  .text-muted-foreground\/80 {
    color: var(--muted-foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-muted-foreground\/80 {
      color: color-mix(in oklab, var(--muted-foreground) 80%, transparent);
    }
  }

  .text-neutral-500 {
    color: var(--color-neutral-500);
  }

  .text-primary {
    color: var(--primary);
  }

  .text-primary-foreground {
    color: var(--primary-foreground);
  }

  .text-secondary-foreground {
    color: var(--secondary-foreground);
  }

  .text-transparent {
    color: rgba(0, 0, 0, 0);
  }

  .text-white {
    color: var(--color-white);
  }

  .text-white\/40 {
    color: rgba(255, 255, 255, .4);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-white\/40 {
      color: color-mix(in oklab, var(--color-white) 40%, transparent);
    }
  }

  .text-white\/60 {
    color: rgba(255, 255, 255, .6);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-white\/60 {
      color: color-mix(in oklab, var(--color-white) 60%, transparent);
    }
  }

  .uppercase {
    text-transform: uppercase;
  }

  .no-underline {
    -webkit-text-decoration-line: none;
    text-decoration-line: none;
  }

  .underline-offset-4 {
    text-underline-offset: 4px;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .shadow-\[0_0_0_1px_rgba\(0\,0\,0\,0\.08\)\,0px_1px_2px_rgba\(0\,0\,0\,0\.04\)\] {
    --tw-shadow: 0 0 0 1px var(--tw-shadow-color, rgba(0, 0, 0, .08)), 0px 1px 2px var(--tw-shadow-color, rgba(0, 0, 0, .04));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0_0_1px_1px_rgba\(255\,255\,255\,0\.08\)_inset\,0_1px_1\.5px_0_rgba\(0\,0\,0\,0\.32\)\,0_0_0_0\.5px_\#1a94ff\] {
    --tw-shadow: 0 0 1px 1px var(--tw-shadow-color, rgba(255, 255, 255, .08)) inset, 0 1px 1.5px 0 var(--tw-shadow-color, rgba(0, 0, 0, .32)), 0 0 0 .5px var(--tw-shadow-color, #1a94ff);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0_8px_32px_0_rgba\(0\,0\,0\,0\.1\)\] {
    --tw-shadow: 0 8px 32px 0 var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0_8px_32px_0_rgba\(255\,255\,255\,0\.1\)\] {
    --tw-shadow: 0 8px 32px 0 var(--tw-shadow-color, rgba(255, 255, 255, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0px_0px_0px_1px_rgba\(0\,0\,0\,\.07\)\,0px_0px_0px_3px_\#fff\,0px_0px_0px_4px_rgba\(0\,0\,0\,\.08\)\] {
    --tw-shadow: 0px 0px 0px 1px var(--tw-shadow-color, rgba(0, 0, 0, .07)), 0px 0px 0px 3px var(--tw-shadow-color, #fff), 0px 0px 0px 4px var(--tw-shadow-color, rgba(0, 0, 0, .08));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0px_1px_1px_0px_rgba\(0\,0\,0\,0\.05\)\,0px_1px_1px_0px_rgba\(255\,252\,240\,0\.5\)_inset\,0px_0px_0px_1px_hsla\(0\,0\%\,100\%\,0\.1\)_inset\,0px_0px_1px_0px_rgba\(28\,27\,26\,0\.5\)\] {
    --tw-shadow: 0px 1px 1px 0px var(--tw-shadow-color, rgba(0, 0, 0, .05)), 0px 1px 1px 0px var(--tw-shadow-color, rgba(255, 252, 240, .5)) inset, 0px 0px 0px 1px var(--tw-shadow-color, rgba(255, 255, 255, .1)) inset, 0px 0px 1px 0px var(--tw-shadow-color, rgba(28, 27, 26, .5));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 1px 2px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xs {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgba(0, 0, 0, .05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[rgba\(17\,24\,28\,0\.08\)_0_0_0_1px\,rgba\(17\,24\,28\,0\.08\)_0_1px_2px_-1px\,rgba\(17\,24\,28\,0\.04\)_0_2px_4px\] {
    --tw-shadow-color: rgba(17, 24, 28, .08);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-\[rgba\(17\,24\,28\,0\.08\)_0_0_0_1px\,rgba\(17\,24\,28\,0\.08\)_0_1px_2px_-1px\,rgba\(17\,24\,28\,0\.04\)_0_2px_4px\] {
      --tw-shadow-color: color-mix(in oklab, rgba(17, 24, 28, .08) 0 0 0 1px, rgba(17, 24, 28, .08) 0 1px 2px -1px, rgba(17, 24, 28, .04) 0 2px 4px var(--tw-shadow-alpha), transparent);
    }
  }

  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-3xl {
    --tw-blur: blur(var(--blur-3xl));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .backdrop-blur-\[2px\] {
    --tw-backdrop-blur: blur(2px);
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[color\,box-shadow\] {
    transition-property: color, box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }

  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (hover: hover) {
    .group-hover\:block:is(:where(.group):hover *) {
      display: block;
    }
  }

  @media (hover: hover) {
    .group-hover\:w-full:is(:where(.group):hover *) {
      width: 100%;
    }
  }

  .after\:absolute:after {
    content: var(--tw-content);
    position: absolute;
  }

  .after\:inset-0:after {
    content: var(--tw-content);
    inset: calc(var(--spacing) * 0);
  }

  .after\:rounded-full:after {
    content: var(--tw-content);
    border-radius: 3.40282e38px;
  }

  .after\:bg-\[radial-gradient\(circle_at_50\%_50\%\,rgba\(0\,0\,0\,0\.2\)\,transparent_70\%\)\]:after {
    content: var(--tw-content);
    background-image: radial-gradient(circle, rgba(0, 0, 0, .2), rgba(0, 0, 0, 0) 70%);
  }

  .after\:bg-\[radial-gradient\(circle_at_50\%_50\%\,rgba\(255\,255\,255\,0\.2\)\,transparent_70\%\)\]:after {
    content: var(--tw-content);
    background-image: radial-gradient(circle, rgba(255, 255, 255, .2), rgba(0, 0, 0, 0) 70%);
  }

  @media (hover: hover) {
    .hover\:scale-110:hover {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:bg-accent:hover {
      background-color: var(--accent);
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-700:hover {
      background-color: var(--color-blue-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-destructive\/90:hover {
      background-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-destructive\/90:hover {
        background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-neutral-100:hover {
      background-color: var(--color-neutral-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary\/90:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, var(--primary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-secondary\/80:hover {
      background-color: var(--secondary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-secondary\/80:hover {
        background-color: color-mix(in oklab, var(--secondary) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:text-accent-foreground:hover {
      color: var(--accent-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-foreground:hover {
      color: var(--foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-red-800:hover {
      color: var(--color-red-800);
    }
  }

  @media (hover: hover) {
    .hover\:underline:hover {
      -webkit-text-decoration-line: underline;
      text-decoration-line: underline;
    }
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  .focus-visible\:border-ring:focus-visible {
    border-color: var(--ring);
  }

  .focus-visible\:ring-\[3px\]:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-destructive\/20:focus-visible {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-destructive\/20:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .focus-visible\:ring-ring\/50:focus-visible {
    --tw-ring-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-ring\/50:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  .disabled\:pointer-events-none:disabled {
    pointer-events: none;
  }

  .disabled\:opacity-50:disabled {
    opacity: .5;
  }

  .has-\[\>svg\]\:px-2\.5:has( > svg) {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .has-\[\>svg\]\:px-3:has( > svg) {
    padding-inline: calc(var(--spacing) * 3);
  }

  .has-\[\>svg\]\:px-4:has( > svg) {
    padding-inline: calc(var(--spacing) * 4);
  }

  .aria-invalid\:border-destructive[aria-invalid="true"] {
    border-color: var(--destructive);
  }

  .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .data-\[success\]\:text-transparent[data-success] {
    color: rgba(0, 0, 0, 0);
  }

  @media (min-width: 40rem) {
    .sm\:text-6xl {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-7xl {
      font-size: var(--text-7xl);
      line-height: var(--tw-leading, var(--text-7xl--line-height));
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-lg {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:top-\[10\%\] {
      top: 10%;
    }
  }

  @media (min-width: 48rem) {
    .md\:top-\[15\%\] {
      top: 15%;
    }
  }

  @media (min-width: 48rem) {
    .md\:top-\[20\%\] {
      top: 20%;
    }
  }

  @media (min-width: 48rem) {
    .md\:top-\[75\%\] {
      top: 75%;
    }
  }

  @media (min-width: 48rem) {
    .md\:right-\[0\%\] {
      right: 0%;
    }
  }

  @media (min-width: 48rem) {
    .md\:right-\[20\%\] {
      right: 20%;
    }
  }

  @media (min-width: 48rem) {
    .md\:bottom-\[10\%\] {
      bottom: 10%;
    }
  }

  @media (min-width: 48rem) {
    .md\:left-\[-5\%\] {
      left: -5%;
    }
  }

  @media (min-width: 48rem) {
    .md\:left-\[10\%\] {
      left: 10%;
    }
  }

  @media (min-width: 48rem) {
    .md\:left-\[25\%\] {
      left: 25%;
    }
  }

  @media (min-width: 48rem) {
    .md\:mb-8 {
      margin-bottom: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 48rem) {
    .md\:mb-12 {
      margin-bottom: calc(var(--spacing) * 12);
    }
  }

  @media (min-width: 48rem) {
    .md\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 48rem) {
    .md\:text-8xl {
      font-size: var(--text-8xl);
      line-height: var(--tw-leading, var(--text-8xl--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:text-xl {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }

  .dark\:scale-0:is(.dark *) {
    --tw-scale-x: 0%;
    --tw-scale-y: 0%;
    --tw-scale-z: 0%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .dark\:scale-100:is(.dark *) {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .dark\:-rotate-90:is(.dark *) {
    rotate: -90deg;
  }

  .dark\:rotate-0:is(.dark *) {
    rotate: none;
  }

  .dark\:border-input:is(.dark *) {
    border-color: var(--input);
  }

  .dark\:border-white\/\[0\.15\]:is(.dark *) {
    border-color: rgba(255, 255, 255, .15);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-white\/\[0\.15\]:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-white) 15%, transparent);
    }
  }

  .dark\:bg-\[\#030303\]:is(.dark *) {
    background-color: #030303;
  }

  .dark\:bg-\[\#121212\]:is(.dark *) {
    background-color: #121212;
  }

  .dark\:bg-destructive\/60:is(.dark *) {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-destructive\/60:is(.dark *) {
      background-color: color-mix(in oklab, var(--destructive) 60%, transparent);
    }
  }

  .dark\:bg-gray-700:is(.dark *) {
    background-color: var(--color-gray-700);
  }

  .dark\:bg-gray-800:is(.dark *) {
    background-color: var(--color-gray-800);
  }

  .dark\:bg-gray-900:is(.dark *) {
    background-color: var(--color-gray-900);
  }

  .dark\:bg-input\/30:is(.dark *) {
    background-color: var(--input);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-input\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--input) 30%, transparent);
    }
  }

  .dark\:bg-neutral-800:is(.dark *) {
    background-color: var(--color-neutral-800);
  }

  .dark\:bg-red-400:is(.dark *) {
    background-color: var(--color-red-400);
  }

  .dark\:bg-zinc-900:is(.dark *) {
    background-color: var(--color-zinc-900);
  }

  .dark\:from-\[\#030303\]:is(.dark *) {
    --tw-gradient-from: #030303;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:via-transparent:is(.dark *) {
    --tw-gradient-via: transparent;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .dark\:to-\[\#030303\]\/80:is(.dark *) {
    --tw-gradient-to: rgba(3, 3, 3, .8);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color(display-p3 0 0 0)) {
    .dark\:to-\[\#030303\]\/80:is(.dark *) {
      --tw-gradient-to: color(display-p3 .0117647 .0117647 .0117647 / .8);
    }
  }

  @supports (color: lab(0% 0 0)) {
    .dark\:to-\[\#030303\]\/80:is(.dark *) {
      --tw-gradient-to: lab(.822525% 0 0 / .8);
    }
  }

  .dark\:text-white:is(.dark *) {
    color: var(--color-white);
  }

  .dark\:text-white\/90:is(.dark *) {
    color: rgba(255, 255, 255, .9);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:text-white\/90:is(.dark *) {
      color: color-mix(in oklab, var(--color-white) 90%, transparent);
    }
  }

  .dark\:shadow-\[0_1px_0_0_rgba\(255\,255\,255\,0\.03\)_inset\,0_0_0_1px_rgba\(255\,255\,255\,0\.03\)_inset\,0_0_0_1px_rgba\(0\,0\,0\,0\.1\)\,0_2px_2px_0_rgba\(0\,0\,0\,0\.1\)\,0_4px_4px_0_rgba\(0\,0\,0\,0\.1\)\,0_8px_8px_0_rgba\(0\,0\,0\,0\.1\)\]:is(.dark *) {
    --tw-shadow: 0 1px 0 0 var(--tw-shadow-color, rgba(255, 255, 255, .03)) inset, 0 0 0 1px var(--tw-shadow-color, rgba(255, 255, 255, .03)) inset, 0 0 0 1px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 2px 2px 0 var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 4px 4px 0 var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 8px 8px 0 var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .dark\:shadow-\[0_8px_32px_0_rgba\(255\,255\,255\,0\.1\)\]:is(.dark *) {
    --tw-shadow: 0 8px 32px 0 var(--tw-shadow-color, rgba(255, 255, 255, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .dark\:shadow-\[0px_0px_0px_1px_rgba\(0\,0\,0\,\.07\)\,0px_0px_0px_3px_rgba\(100\,100\,100\,0\.3\)\,0px_0px_0px_4px_rgba\(0\,0\,0\,\.08\)\]:is(.dark *) {
    --tw-shadow: 0px 0px 0px 1px var(--tw-shadow-color, rgba(0, 0, 0, .07)), 0px 0px 0px 3px var(--tw-shadow-color, rgba(100, 100, 100, .3)), 0px 0px 0px 4px var(--tw-shadow-color, rgba(0, 0, 0, .08));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .dark\:shadow-\[0px_1px_1px_0px_rgba\(0\,0\,0\,0\.15\)\,0px_1px_1px_0px_rgba\(0\,0\,0\,0\.15\)_inset\,0px_0px_0px_1px_rgba\(0\,0\,0\,0\.15\)_inset\,0px_0px_1px_0px_rgba\(0\,0\,0\,0\.15\)\]:is(.dark *) {
    --tw-shadow: 0px 1px 1px 0px var(--tw-shadow-color, rgba(0, 0, 0, .15)), 0px 1px 1px 0px var(--tw-shadow-color, rgba(0, 0, 0, .15)) inset, 0px 0px 0px 1px var(--tw-shadow-color, rgba(0, 0, 0, .15)) inset, 0px 0px 1px 0px var(--tw-shadow-color, rgba(0, 0, 0, .15));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .dark\:after\:bg-\[radial-gradient\(circle_at_50\%_50\%\,rgba\(255\,255\,255\,0\.2\)\,transparent_70\%\)\]:is(.dark *):after {
    content: var(--tw-content);
    background-image: radial-gradient(circle, rgba(255, 255, 255, .2), rgba(0, 0, 0, 0) 70%);
  }

  @media (hover: hover) {
    .dark\:hover\:bg-accent\/50:is(.dark *):hover {
      background-color: var(--accent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-accent\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--accent) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-input\/50:is(.dark *):hover {
      background-color: var(--input);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-input\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--input) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-neutral-800\/80:is(.dark *):hover {
      background-color: rgba(38, 38, 38, .8);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-neutral-800\/80:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--color-neutral-800) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-red-400:is(.dark *):hover {
      color: var(--color-red-400);
    }
  }

  .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
    }
  }

  .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
    }
  }

  .\[\&_svg\]\:pointer-events-none svg {
    pointer-events: none;
  }

  .\[\&_svg\]\:shrink-0 svg {
    flex-shrink: 0;
  }

  .\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*="size-"]) {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\&\>svg\]\:pointer-events-none > svg {
    pointer-events: none;
  }

  .\[\&\>svg\]\:size-3 > svg {
    width: calc(var(--spacing) * 3);
    height: calc(var(--spacing) * 3);
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-accent:hover {
      background-color: var(--accent);
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-destructive\/90:hover {
      background-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-destructive\/90:hover {
        background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-primary\/90:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, var(--primary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-secondary\/90:hover {
      background-color: var(--secondary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-secondary\/90:hover {
        background-color: color-mix(in oklab, var(--secondary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:text-accent-foreground:hover {
      color: var(--accent-foreground);
    }
  }
}

@property --tw-animation-delay {
  syntax: "*";
  inherits: false;
  initial-value: 0s;
}

@property --tw-animation-direction {
  syntax: "*";
  inherits: false;
  initial-value: normal;
}

@property --tw-animation-duration {
  syntax: "*";
  inherits: false
}

@property --tw-animation-fill-mode {
  syntax: "*";
  inherits: false;
  initial-value: none;
}

@property --tw-animation-iteration-count {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-enter-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-enter-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-exit-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-exit-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@font-face {
  font-family: skiper;
  src: url("/fonts/myFont.ttf") format("truetype");
  font-weight: 1000;
  font-style: normal;
}

:root {
  --radius: .625rem;
  --background: #fff;
  --foreground: #0a0a0a;
  --card: #fff;
  --card-foreground: #0a0a0a;
  --popover: #fff;
  --popover-foreground: #0a0a0a;
  --primary: #171717;
  --primary-foreground: #fafafa;
  --secondary: #f5f5f5;
  --secondary-foreground: #171717;
  --muted: #f5f5f5;
  --muted-foreground: #737373;
  --accent: #f5f5f5;
  --accent-foreground: #171717;
  --destructive: #e40014;
  --border: #e5e5e5;
  --input: #e5e5e5;
  --ring: #a1a1a1;
  --chart-1: #f05100;
  --chart-2: #009588;
  --chart-3: #104e64;
  --chart-4: #fcbb00;
  --chart-5: #f99c00;
  --sidebar: #fafafa;
  --sidebar-foreground: #0a0a0a;
  --sidebar-primary: #171717;
  --sidebar-primary-foreground: #fafafa;
  --sidebar-accent: #f5f5f5;
  --sidebar-accent-foreground: #171717;
  --sidebar-border: #e5e5e5;
  --sidebar-ring: #a1a1a1;
}

@supports (color: color(display-p3 0 0 0)) {
  :root {
    --background: color(display-p3 1 1 1);
    --foreground: color(display-p3 .0393882 .0393882 .0393882);
    --card: color(display-p3 1 1 1);
    --card-foreground: color(display-p3 .0393882 .0393882 .0393882);
    --popover: color(display-p3 1 1 1);
    --popover-foreground: color(display-p3 .0393882 .0393882 .0393882);
    --primary: color(display-p3 .0905274 .0905274 .0905274);
    --primary-foreground: color(display-p3 .980256 .980256 .980256);
    --secondary: color(display-p3 .960587 .960587 .960587);
    --secondary-foreground: color(display-p3 .0905274 .0905274 .0905274);
    --muted: color(display-p3 .960587 .960587 .960587);
    --muted-foreground: color(display-p3 .451519 .451519 .451519);
    --accent: color(display-p3 .960587 .960587 .960587);
    --accent-foreground: color(display-p3 .0905274 .0905274 .0905274);
    --destructive: color(display-p3 .830323 .140383 .133196);
    --border: color(display-p3 .898161 .898161 .898161);
    --input: color(display-p3 .898161 .898161 .898161);
    --ring: color(display-p3 .630163 .630163 .630163);
    --chart-1: color(display-p3 .887467 .341665 .0219962);
    --chart-2: color(display-p3 .207114 .579584 .53668);
    --chart-3: color(display-p3 .142586 .302008 .385094);
    --chart-4: color(display-p3 .959186 .738519 .118268);
    --chart-5: color(display-p3 .93994 .620584 .0585367);
    --sidebar: color(display-p3 .980256 .980256 .980256);
    --sidebar-foreground: color(display-p3 .0393882 .0393882 .0393882);
    --sidebar-primary: color(display-p3 .0905274 .0905274 .0905274);
    --sidebar-primary-foreground: color(display-p3 .980256 .980256 .980256);
    --sidebar-accent: color(display-p3 .960587 .960587 .960587);
    --sidebar-accent-foreground: color(display-p3 .0905274 .0905274 .0905274);
    --sidebar-border: color(display-p3 .898161 .898161 .898161);
    --sidebar-ring: color(display-p3 .630163 .630163 .630163);
  }
}

@supports (color: lab(0% 0 0)) {
  :root {
    --background: lab(100% 0 0);
    --foreground: lab(2.75381% 0 0);
    --card: lab(100% 0 0);
    --card-foreground: lab(2.75381% 0 0);
    --popover: lab(100% 0 0);
    --popover-foreground: lab(2.75381% 0 0);
    --primary: lab(7.78201% -.0000149012 0);
    --primary-foreground: lab(98.26% 0 0);
    --secondary: lab(96.52% -.0000596046 0);
    --secondary-foreground: lab(7.78201% -.0000149012 0);
    --muted: lab(96.52% -.0000596046 0);
    --muted-foreground: lab(48.496% 0 0);
    --accent: lab(96.52% -.0000596046 0);
    --accent-foreground: lab(7.78201% -.0000149012 0);
    --destructive: lab(48.4493% 77.4328 61.5452);
    --border: lab(90.952% -.0000596046 0);
    --input: lab(90.952% -.0000596046 0);
    --ring: lab(66.128% -.0000298023 .0000119209);
    --chart-1: lab(57.1026% 64.2584 89.8886);
    --chart-2: lab(55.0223% -41.0774 -3.90277);
    --chart-3: lab(30.372% -13.1853 -18.7887);
    --chart-4: lab(80.1641% 16.6016 99.2089);
    --chart-5: lab(72.7183% 31.8672 97.9407);
    --sidebar: lab(98.26% 0 0);
    --sidebar-foreground: lab(2.75381% 0 0);
    --sidebar-primary: lab(7.78201% -.0000149012 0);
    --sidebar-primary-foreground: lab(98.26% 0 0);
    --sidebar-accent: lab(96.52% -.0000596046 0);
    --sidebar-accent-foreground: lab(7.78201% -.0000149012 0);
    --sidebar-border: lab(90.952% -.0000596046 0);
    --sidebar-ring: lab(66.128% -.0000298023 .0000119209);
  }
}

.dark {
  --background: #222;
  --foreground: #eee;
  --card: #2e2e2e;
  --card-foreground: #eee;
  --popover: #2e2e2e;
  --popover-foreground: #eee;
  --primary: #dedede;
  --primary-foreground: #2e2e2e;
  --secondary: #3a3a3a;
  --secondary-foreground: #eee;
  --muted: #3a3a3a;
  --muted-foreground: #9e9e9e;
  --accent: #3a3a3a;
  --accent-foreground: #eee;
  --destructive: #ff6568;
  --border: rgba(255, 255, 255, .1);
  --input: rgba(255, 255, 255, .15);
  --ring: #737373;
  --chart-1: #1447e6;
  --chart-2: #00bb7f;
  --chart-3: #f99c00;
  --chart-4: #ac4bff;
  --chart-5: #ff2357;
  --sidebar: #2e2e2e;
  --sidebar-foreground: #eee;
  --sidebar-primary: #1447e6;
  --sidebar-primary-foreground: #eee;
  --sidebar-accent: #3a3a3a;
  --sidebar-accent-foreground: #eee;
  --sidebar-border: rgba(255, 255, 255, .1);
  --sidebar-ring: #737373;
}

@supports (color: color(display-p3 0 0 0)) {
  .dark {
    --background: color(display-p3 .131499 .131499 .131499);
    --foreground: color(display-p3 .93448 .93448 .93448);
    --card: color(display-p3 .179236 .179236 .179236);
    --card-foreground: color(display-p3 .93448 .93448 .93448);
    --popover: color(display-p3 .179236 .179236 .179236);
    --popover-foreground: color(display-p3 .93448 .93448 .93448);
    --primary: color(display-p3 .869816 .869817 .869816);
    --primary-foreground: color(display-p3 .179236 .179236 .179236);
    --secondary: color(display-p3 .229013 .229013 .229013);
    --secondary-foreground: color(display-p3 .93448 .93448 .93448);
    --muted: color(display-p3 .229013 .229013 .229013);
    --muted-foreground: color(display-p3 .620499 .620499 .620499);
    --accent: color(display-p3 .229013 .229013 .229013);
    --accent-foreground: color(display-p3 .93448 .93448 .93448);
    --destructive: color(display-p3 .933534 .431676 .423491);
    --border: color(display-p3 1 1 1 / .1);
    --input: color(display-p3 1 1 1 / .15);
    --ring: color(display-p3 .451519 .451519 .451519);
    --chart-1: color(display-p3 .1379 .274983 .867624);
    --chart-2: color(display-p3 .267113 .726847 .508397);
    --chart-3: color(display-p3 .93994 .620584 .0585367);
    --chart-4: color(display-p3 .629519 .30089 .990817);
    --chart-5: color(display-p3 .921824 .240748 .355666);
    --sidebar: color(display-p3 .179236 .179236 .179236);
    --sidebar-foreground: color(display-p3 .93448 .93448 .93448);
    --sidebar-primary: color(display-p3 .1379 .274983 .867624);
    --sidebar-primary-foreground: color(display-p3 .93448 .93448 .93448);
    --sidebar-accent: color(display-p3 .229013 .229013 .229013);
    --sidebar-accent-foreground: color(display-p3 .93448 .93448 .93448);
    --sidebar-border: color(display-p3 1 1 1 / .1);
    --sidebar-ring: color(display-p3 .451519 .451519 .451519);
  }
}

@supports (color: lab(0% 0 0)) {
  .dark {
    --background: lab(13% 0 0);
    --foreground: lab(94.2% 0 0);
    --card: lab(18.8% 0 0);
    --card-foreground: lab(94.2% 0 0);
    --popover: lab(18.8% 0 0);
    --popover-foreground: lab(94.2% 0 0);
    --primary: lab(88.4% 0 .0000119209);
    --primary-foreground: lab(18.8% 0 0);
    --secondary: lab(24.6% 0 0);
    --secondary-foreground: lab(94.2% 0 0);
    --muted: lab(24.6% 0 0);
    --muted-foreground: lab(65.2% 0 0);
    --accent: lab(24.6% 0 0);
    --accent-foreground: lab(94.2% 0 0);
    --destructive: lab(63.7053% 60.7449 31.3109);
    --border: lab(100% 0 0 / .1);
    --input: lab(100% 0 0 / .15);
    --ring: lab(48.496% 0 0);
    --chart-1: lab(36.9089% 35.0961 -85.6872);
    --chart-2: lab(66.9756% -58.27 19.5419);
    --chart-3: lab(72.7183% 31.8672 97.9407);
    --chart-4: lab(52.0183% 66.11 -78.2316);
    --chart-5: lab(56.101% 79.4329 31.4532);
    --sidebar: lab(18.8% 0 0);
    --sidebar-foreground: lab(94.2% 0 0);
    --sidebar-primary: lab(36.9089% 35.0961 -85.6872);
    --sidebar-primary-foreground: lab(94.2% 0 0);
    --sidebar-accent: lab(24.6% 0 0);
    --sidebar-accent-foreground: lab(94.2% 0 0);
    --sidebar-border: lab(100% 0 0 / .1);
    --sidebar-ring: lab(48.496% 0 0);
  }
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

@property --tw-content {
  syntax: "*";
  inherits: false;
  initial-value: "";
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/*# sourceMappingURL=%5Broot-of-the-server%5D__8ebb6d4b._.css.map*/