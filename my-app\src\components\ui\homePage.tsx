"use client";

import React from "react";
import Image from "next/image";
import FlipLink from "@/components/ui/text-effect-flipper";
import dynamic from "next/dynamic";

const HomePage = () => {
  return (
    <div className="relative min-h-screen overflow-hidden">
      <h1 className="text-6xl flex items-center justify-center mt-100 font-bold  ">
        I&apos;m
        <Image
          src="/profilePhoto.jpg"
          alt="Profile photo of Parth Chauhan"
          width={80}
          height={80}
          className="w-20 h-15 rounded-xl  mx-2 object-cover"
        />
        Parth Chauhan
      </h1>
      <h2 className="text-7xl flex items-center justify-center mt-4 font-bold font-[skiper] ">
        Bringing Ideas to
        <pre> </pre>
        <span className="text-black bg-yellow-300 px-2 py-1 rounded-lg">Reality</span>
      </h2>
    </div>
  );
};

export default HomePage;
