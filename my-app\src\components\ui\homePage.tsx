"use client";

import React from "react";
import Image from "next/image";
import FlipLink from "@/components/ui/text-effect-flipper";
import dynamic from "next/dynamic";

const HomePage = () => {
  return (
    <div className="relative min-h-screen overflow-hidden">
      <h1 className="font-my-font text-black text-7xl flex items-center justify-center mt-100">
        I&apos;m
        <Image
          src="/profilePhoto.jpg"
          alt="Profile photo of Parth Chauhan"
          width={80}
          height={80}
          className="w-20 h-15 rounded-xl  mx-2 object-cover"
        />
        <PERSON><PERSON> Chauhan
      </h1>
      <h2 className="text-black text-7xl flex items-center justify-center mt-4 font-bold">
        Bringing Ideas to reality
      </h2>
    </div>
  );
};

export default HomePage;
