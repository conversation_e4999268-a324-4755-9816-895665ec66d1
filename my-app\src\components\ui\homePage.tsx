"use client";

import React from "react";
import Image from "next/image";
import FlipLink from "@/components/ui/text-effect-flipper";
import dynamic from "next/dynamic";

const HomePage = () => {
  return (
    <div className="relative min-h-screen overflow-hidden px-4 sm:px-6 lg:px-8">
      {/* Main heading with responsive text sizes and layout */}
      <h1
        className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl
                     flex flex-col sm:flex-row items-center justify-center
                     mt-20 sm:mt-24 md:mt-32 lg:mt-40 xl:mt-100
                     font-bold text-center gap-2 sm:gap-0"
      >
        <span className="mb-2 sm:mb-0 sm:mr-2">I&apos;m</span>
        <Image
          src="/profilePhoto.jpg"
          alt="Profile photo of Parth Chauhan"
          width={80}
          height={80}
          className="w-12 h-12 sm:w-16 sm:h-16 md:w-18 md:h-18 lg:w-20 lg:h-20
                     rounded-xl mx-2 object-cover flex-shrink-0"
        />
        <span className="mt-2 sm:mt-0 sm:ml-2">Parth Chauhan</span>
      </h1>

      {/* Secondary heading with responsive text and layout */}
      <h2
        className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl
                     flex flex-col sm:flex-row items-center justify-center
                     mt-4 sm:mt-6 md:mt-8 lg:mt-10
                     font-bold font-[skiper] text-center gap-2 sm:gap-1
                     px-2 sm:px-4"
      >
        <span className="mb-2 sm:mb-0">Bringing Ideas to</span>
        <span
          className="text-black bg-yellow-300 px-2 py-1 sm:px-3 sm:py-2
                         rounded-lg text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl 2xl:text-6xl
                         whitespace-nowrap"
        >
          Reality
        </span>
      </h2>
    </div>
  );
};

export default HomePage;
